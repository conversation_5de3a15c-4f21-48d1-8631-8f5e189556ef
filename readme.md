## Overview
Simple producer/consumer demo based on Stream Protocol framework.
The demo uses Java 11 and Spring Boot 2.7

## Prerequisites
* Java - 11.0.17
* Scala - 2.11.12
* Docker

## Starting the service
* Run the docker-compose.yaml file
* Run the market-aggregator-producer in IntelliJ with the provided ./run configuration 
* Run the market-aggregator-consumer in IntelliJ with the provided ./run configuration

## Producing messages
* Please check the attached postman collection _StreamProtocol-ProducerContainer.postman_collection.json_ 