version: '3.7'
services:
  mysql:
    container_name: mysql
    image: mysql:8.0.32
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: 'my-secret-pw'
      MYSQL_DATABASE: 'SP'
      MYSQL_USER: 'user'
      MYSQL_PASSWORD: 'pass'
  zookeeper:
    container_name: zookeeper
    image: wurstmeister/zookeeper:latest
    ports:
      - "2181:2181"
  kafka:
    container_name: kafka
    depends_on:
      - zookeeper
    image: wurstmeister/kafka:2.13-2.8.1
    ports:
      - "9092:9092"
      - "29092:29092"
    environment:
      KAFKA_LISTENERS: INNER://:29092,OUTER://:9092
      KAFKA_ADVERTISED_LISTENERS: INNER://kafka:29092,OUTER://localhost:9092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: INNER:PLAINTEXT,OUTER:PLAINTEXT
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      K<PERSON>KA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_INTER_BROKER_LISTENER_NAME: INNER
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
  docker-setup-kafka:
    container_name: docker-setup-kafka
    image: wurstmeister/kafka:2.13-2.8.1
    depends_on:
      - kafka
    entrypoint: [ '/bin/sh', '-c' ]
    command: |
      "
      echo -e 'waiting for kafka to become reachable'
      kafka-topics.sh --bootstrap-server kafka:29092 --list
      echo -e 'Creating kafka topics'
      kafka-topics.sh --create --topic test-topic-it-metadata --partitions 1 --replication-factor 1 --bootstrap-server kafka:29092
      kafka-topics.sh --create --topic test-topic-it --partitions 1 --replication-factor 1 --bootstrap-server kafka:29092
      echo -e 'Listing topics created'
      kafka-topics.sh --bootstrap-server kafka:29092 --list
      "