## StreamProtocolEnvelope:

protocolHeaders {
  headers {
    key: "protcol_header_key1"
    value: "protcol_header_value1"
  }
  headers {
    key: "protcol_header_key2"
    value: "protcol_header_value2"
  }
}
applicationHeaders {
  headers {
    key: "app_header_key1"
    value: "app_header_value1"
  }
  headers {
    key: "app_header_key2"
    value: "app_header_value2"
  }
}
payload: "\000\001\000\001\000\001\000\001\000\000\000\000\000\000\000\001\001\n\022\n\004e.s1\022\004e.i1\032\004e.r1R\231\001\n\022\n\004m.s1\022\004m.i1\032\004m.r1@\001J\200\001\022\025\n\005md.s1\022\005md.i1\032\005md.r1\242\006f\nd\n\00251\022\000\032\006Sports\"\002ES*\tMasculino2\aAmateur8\006B\f\b\335\316\305\240\006\020\230\253\201\361\002J\001SR\016event1, event2Z\031Multi League - Odds Boost"

## Payload first 17 bits:
protocol: 1, version: 1, msgType: 1, msgVersion: 1, streamProtocolVersion: 1, reset: true

## Payload MarketChanges:
eventId {
  supplier: "e.s1"
  identifier: "e.i1"
  ramp: "e.r1"
}
marketChanges {
  marketId {
    supplier: "m.s1"
    identifier: "m.i1"
    ramp: "m.r1"
  }
  type: UPDATE
  marketDefinition {
    marketDefId {
      supplier: "md.s1"
      identifier: "md.i1"
      ramp: "md.r1"
    }
    mappings {
      spain_mapping {
        dgoj_sport_code: "51"
        dgoj_sport_name: ""
        dgoj_event_type: "Sports"
        dgoj_competition_country_iso: "ES"
        dgoj_competition_gender: "Masculino"
        dgoj_competition_category: "Amateur"
        event_status: EVENT_STATUS_COMPLETED
        event_end_date {
          seconds: 1678862173
          nanos: 773871000
        }
        dgoj_special_event: "S"
        multi_event_leg: "event1, event2"
        multi_competition_leg: "Multi League - Odds Boost"
      }
    }
  }
}