//package demo;
//
//import com.google.protobuf.ByteString;
//import com.isp.platform.mas.proto.StreamProtocolMessage.StreamProtocolEnvelope;
//
//import java.io.FileInputStream;
//import java.io.FileOutputStream;
//import java.io.IOException;
//import java.net.URISyntaxException;
//import java.net.URL;
//import java.nio.ByteBuffer;
//import java.nio.file.Files;
//import java.nio.file.Path;
//import java.nio.file.StandardOpenOption;
//import java.time.Instant;
//
//import static com.isp.platform.mas.messages.Common.*;
//import static com.isp.platform.mas.messages.MarketChangesProtocolBuffer.MarketChange;
//import static com.isp.platform.mas.messages.MarketChangesProtocolBuffer.MarketChanges;
//import static com.isp.platform.mas.proto.StreamProtocolMessage.ApplicationHeaders;
//import static com.isp.platform.mas.proto.StreamProtocolMessage.ProtocolHeaders;
//import static java.lang.System.arraycopy;
//
//public final class App {
//
//    private static final URL kafkaMsgOutputResource = App.class.getResource("/kafka-msg.output");
//
//    public static void main(String[] args) throws IOException, URISyntaxException {
//        prepareKafkaMsgOutputResource();
//
//        // Build message and write to file
//        MarketChanges.Builder demoMarketChangesBuilder = MarketChangeFactory.createMarketChangesBuilder();
//        MarketChanges demoMarketChanges = demoMarketChangesBuilder.build();
//
//        StreamProtocolEnvelope.Builder demoEnvelopeBuilder = MarketChangeFactory.createEnvelopeBuilder();
//        demoEnvelopeBuilder.setPayload(wrap(demoMarketChanges.toByteArray()));
//        StreamProtocolEnvelope demoStreamProtocolEnvelope = demoEnvelopeBuilder.build();
//
//        demoStreamProtocolEnvelope.writeTo(new FileOutputStream(kafkaMsgOutputResource.getFile()));
//
//        // Parse message and print to console
//        StreamProtocolEnvelope streamProtocolEnvelope =
//                StreamProtocolEnvelope.parseFrom(new FileInputStream(kafkaMsgOutputResource.getFile()));
//        System.out.println(streamProtocolEnvelope);
//
//        byte[] marketChangesPayload = unwrap(streamProtocolEnvelope.getPayload().toByteArray());
//        MarketChanges marketChangesDeser = MarketChanges.parseFrom(marketChangesPayload);
//        System.out.println(marketChangesDeser);
//    }
//
//    private static void prepareKafkaMsgOutputResource() throws IOException, URISyntaxException {
//        assert kafkaMsgOutputResource != null;
//        Files.write(
//                Path.of(kafkaMsgOutputResource.toURI()),
//                new byte[0],
//                StandardOpenOption.WRITE,
//                StandardOpenOption.TRUNCATE_EXISTING
//        );
//    }
//
//
//    private static final class MarketChangeFactory {
//
//        public static StreamProtocolEnvelope.Builder createEnvelopeBuilder() {
//            return StreamProtocolEnvelope.newBuilder()
//                    .setApplicationHeaders(createApplicationHeadersBuilder())
//                    .setProtocolHeaders(createProtocolHeadersBuilder());
//        }
//
//        public static ApplicationHeaders.Builder createApplicationHeadersBuilder() {
//            return ApplicationHeaders.newBuilder()
//                    .putHeaders("app_header_key1", "app_header_value1")
//                    .putHeaders("app_header_key2", "app_header_value2");
//        }
//
//        public static ProtocolHeaders.Builder createProtocolHeadersBuilder() {
//            return ProtocolHeaders.newBuilder()
//                    .putHeaders("protcol_header_key1", "protcol_header_value1")
//                    .putHeaders("protcol_header_key2", "protcol_header_value2");
//        }
//
//        public static MarketChanges.Builder createMarketChangesBuilder() {
//            return MarketChanges.newBuilder()
//                    .setEventId(createEntityIdBuilder("e.s1", "e.i1", "e.r1"))
//                    .addMarketChanges(createMarketChangeBuilder());
//        }
//
//        public static MarketChange.Builder createMarketChangeBuilder() {
//            return MarketChange.newBuilder()
//                    .setMarketId(createEntityIdBuilder("m.s1", "m.i1", "m.r1"))
//                    .setType(MarketChange.ChangeType.UPDATE)
//                    .setMarketDefinition(createMarketDefinitionBuilder());
//        }
//
//        public static MarketDefinition.Builder createMarketDefinitionBuilder() {
//            return MarketDefinition.newBuilder()
//                    .setMarketDefId(createEntityIdBuilder("md.s1", "md.i1", "md.r1"))
//                    .setMappings(createMappingsBuilder());
//        }
//
//        public static Mappings.Builder createMappingsBuilder() {
//            return Mappings.newBuilder()
//                    .setSpainMapping(createSpainMappingExtensionBuilder());
//        }
//
//        public static SpainMapping.Builder createSpainMappingExtensionBuilder() {
//            return SpainMapping.newBuilder()
//                    .setDgojSportCode("51")
//                    .setDgojSportName("")
//                    .setDgojEventType("Sports")
//                    .setDgojCompetitionCountryIso("ES")
//                    .setDgojCompetitionGender("Masculino")
//                    .setDgojCompetitionCategory("Amateur")
//                    .setEventStatus(EventStatus.EVENT_STATUS_COMPLETED)
//                    .setEventEndDate(Timestamp.newBuilder()
//                            .setSeconds(Instant.now().getEpochSecond())
//                            .setNanos(Instant.now().getNano())
//                            .build()
//                    )
//                    .setDgojSpecialEvent("S")
//                    .setMultiCompetitionLeg("Multi League - Odds Boost")
//                    .setMultiEventLeg("event1, event2");
//        }
//
//        public static EntityId.Builder createEntityIdBuilder(String supplier, String identifier, String ramp) {
//            return EntityId.newBuilder()
//                    .setSupplier(supplier)
//                    .setIdentifier(identifier)
//                    .setRamp(ramp);
//
//        }
//    }
//
//    private static ByteString wrap(byte[] payload) {
//        byte[] protocolType = toBytes((short) 1);
//        byte[] protocolVersion = toBytes((short) 1);
//        byte[] messageType = toBytes((short) 1);
//        byte[] messageVersion = toBytes((short) 1);
//        byte[] streamProtocolVersion = toBytes(1L);
//        byte[] reset = toBytes(true);
//
//        byte[] output = new byte[17 + payload.length];
//
//        output[0] = protocolType[0];
//        output[1] = protocolType[1];
//        output[2] = protocolVersion[0];
//        output[3] = protocolVersion[1];
//        output[4] = messageType[0];
//        output[5] = messageType[1];
//        output[6] = messageVersion[0];
//        output[7] = messageVersion[1];
//        output[8] = streamProtocolVersion[0];
//        output[9] = streamProtocolVersion[1];
//        output[10] = streamProtocolVersion[2];
//        output[11] = streamProtocolVersion[3];
//        output[12] = streamProtocolVersion[4];
//        output[13] = streamProtocolVersion[5];
//        output[14] = streamProtocolVersion[6];
//        output[15] = streamProtocolVersion[7];
//        output[16] = reset[0];
//
//        System.arraycopy(payload, 0, output, 17, payload.length);
//
//        return ByteString.copyFrom(output);
//    }
//
//    private static byte[] toBytes(boolean isFirstMessageInView) {
//        byte[] result = new byte[1];
//        result[0] = (byte) (isFirstMessageInView ? 1 : 0);
//        return result;
//    }
//
//    private static byte[] toBytes(long messageStreamVersion) {
//        return ByteBuffer.allocate(8).putLong(messageStreamVersion).array();
//    }
//
//
//    private static byte[] toBytes(short data) {
//        return ByteBuffer.allocate(2).putShort(data).array();
//    }
//
//    private static byte[] unwrap(byte[] bytes) {
//        if (bytes == null || bytes.length <= 8) {
//            throw new RuntimeException("Encountered message in unknown format");
//        }
//        // Unpack payload in envelope. Abstract all this out into header parser
//        short protocol = fromBytes(bytes[0], bytes[1]);
//        short version = fromBytes(bytes[2], bytes[3]);
//        short msgType = fromBytes(bytes[4], bytes[5]);
//        short msgVersion = fromBytes(bytes[6], bytes[7]);
//        long streamProtocolVersion = fromBytes(bytes[8], bytes[9], bytes[10], bytes[11], bytes[12], bytes[13], bytes[14], bytes[15]);
//        boolean reset = fromBytes(bytes[16]);
//
//        System.out.println("protocol: " + protocol
//                + ", version: " + version
//                + ", msgType: " + msgType
//                + ", msgVersion: " + msgVersion
//                + ", streamProtocolVersion: " + streamProtocolVersion
//                + ", reset: " + reset);
//
//        int headerLength = 17;
//        byte[] result = new byte[bytes.length - headerLength];
//        arraycopy(bytes, headerLength, result, 0, bytes.length - headerLength);
//        return result;
//    }
//
//    private static boolean fromBytes(byte byte0) {
//        return byte0 != 0;
//    }
//
//    private static short fromBytes(byte byte0, byte byte1) {
//        return ByteBuffer.wrap(new byte[]{byte0, byte1}).getShort();
//    }
//
//    private static long fromBytes(byte byte0, byte byte1, byte byte2, byte byte3, byte byte4, byte byte5, byte byte6, byte byte7) {
//        return ByteBuffer.wrap(new byte[]{byte0, byte1, byte2, byte3, byte4, byte5, byte6, byte7}).getLong();
//    }
//}
