syntax = "proto2";

option java_package = "com.isp.platform.mas.messages";


message EntityId {
    optional string supplier = 1;
    optional string identifier = 2;
    optional string ramp = 3;
}

message MarketDefinition {
    optional int64 inceptionTime = 1; // --> loadTime
    required EntityId marketDefId = 2;
    optional Event event = 3; // --> FmgEvent the event eg Manu vs Ars
    optional Competition competition = 4; // Prem, Carling cup, etc
    optional Category category = 5 [deprecated=true]; //HR, Dogs, International Football, English Football
    optional EventType eventType = 6 [deprecated=true]; // Betfair sport, Racing, Football, VSport, Financial
    optional MarketDescription marketDescription = 7; // --> FmgMarket
    optional MarketState marketState = 8;
    repeated Runner runners = 9; // --> FmgRunner
    optional SportInfo sportInfo = 10; // Event sport info
    optional SportVariant sportVariant = 11; // Sport variant in our hierarchy could be understood as an intermediate level between sport an competition (e.g. American Basketball, European Basketball)
    optional Mappings mappings = 100;
}


message Event {
    required EntityId eventId = 1; // --> Event.id (EntityId.identifier) + Event.externalId (EntityId.supplier)
    optional string eventName = 2;
    optional string venueName = 3;
    optional int64 startTime = 4;
    optional int64 betUntilTime = 5;
    optional int64 suspendTime = 6;
    optional bool suspended = 7;
    enum EventBettingStatus {
        UNKNOWN = 0;
        PRICED = 1;
        OFF = 2;
        RESULTED = 3;
        PENDING = 4;
    }
    optional EventBettingStatus bettingStatus = 8 [default = UNKNOWN]; // How should this be represented? From OBEvent.bettingStatus:PRICED | OFF | RESULTED????
    optional bool turnInPlayEnabled = 9;
    enum Sort {
        UNKNOWN_SORT = 0;
        TOURNAMENT = 1;
        MATCH = 2;
        GROUP = 3;
    }
    optional Sort sort = 10 [default = UNKNOWN_SORT];
    optional bool hidden = 18 [default = true];
    optional string rampId = 19 [deprecated=true];
    optional string eventLocation = 20;
    optional string participantAOrigin = 21;
    optional string participantBOrigin = 22;
    optional string multipleKey = 23;
    repeated int32 linkedTypeRestriction = 24;
    optional string unmappedSort = 25;
}

message Competition {
    required EntityId competitionId = 1;
    optional string competionName = 2; // Prem, Carling cup, etc
}

message Category {
    required EntityId categoryId = 1;
    optional string categoryName = 2; //HR, Dogs, International Football, English Football
    optional string categoryGroup = 3;
}

message EventType {
    required EntityId eventTypeId = 1;
    optional string eventTypeName = 2; //Racing, Football, VSport, Financial
    optional bool hidden = 18 [default = false];
}

message SportInfo {
    required EntityId id = 1;
    optional string name = 2; //e.g. Racing, Football, VSport, Financial
    optional string category = 3; //e.g. HR, Dogs, International Football, English Football
    optional string categoryGroup = 4;
}

message SportVariant {
    required EntityId id = 1;
    optional string name = 2; //American Basketball, European Basketball
}

message MarketDescription { // --> FMG Market
    required EntityId marketDescId = 1;
    optional string exchangeMarketId = 2;
    optional bool startingPriceAvailable = 3;
    optional bool livePriceAvailable = 4;
    optional bool guaranteedPriceAvailable = 5;
    optional bool eachWayAvailable = 6;
    optional bool canTurnInPlay = 7;
    optional int32 numberOfPlaces = 8;
    optional string marketName = 9;
    optional string marketTypeName = 10 [deprecated = true]; // transformed version of OB marketTemplate
    enum MarketBettingType {
        UNKNOWN = 0;
        FIXED_ODDS = 1;
        MOVING_HANDICAP = 2;
    }
    enum LegType {
        SIMPLE_SELECTION = 0;
        TRICAST = 1;
        COMBINATION_TRICAST = 2;
        FORECAST = 3;
        REVERSE_FORECAST = 4;
        COMBINATION_FORECAST = 5;
        SCORECAST = 6;
        WINCAST = 7;
    }
    optional MarketBettingType marketBettingType = 11 [default = UNKNOWN];
    repeated LegType availableLegTypes = 12;
    optional double handicap = 13;
    optional int32 sortOrder = 14;
    optional bool suspended = 15;
    optional int32 numberOfWinners = 16;
    optional string placeFraction = 17;
    optional int64 suspendTime = 18; // market specific suspend time.
    optional bool inPlay = 19;
    optional int32 betDelay = 20;
    repeated MarketGroup marketGroups = 21;
    repeated Regulator regulators = 22;
    repeated RegulatorDetails regulatorDetails = 23;
    repeated Rule4Deduction rule4Deduction = 24;
    optional int32 handicapMakeup = 25;
    optional bool resultConfirmed = 26 [default = false];
    repeated MarketJurisdictionDetails marketJurisdictionDetails = 27;
    optional bool settled = 28 [default = false];
    optional string linkedSportsbookMarketId = 29 [deprecated = true]; // deprecated with introduction of `linkedMarkets`
    optional string rampId = 30 [deprecated=true];
    optional MarketType type = 31;
    repeated MarketLinkDetails linkedMarkets = 32;
    optional string feedsId = 33;
    optional int32 minAccumulators = 34;
    optional int32 maxAccumulators = 35;
}

message MarketJurisdictionDetails {
    optional Jurisdiction jurisdiction = 1;
    enum JurisdictionStatus {
        JURISDICTION_UNKNOWN = 0;
        JURISDICTION_ACTIVE = 1;
        JURISDICTION_SUSPENDED = 2;
    }
    optional JurisdictionStatus jurisdictationStatus = 2 [default = JURISDICTION_UNKNOWN];
    optional int32 handicapMakeup = 3;
    optional bool resultConfirmed = 4;
    optional bool settled = 5;
    optional bool resultSet = 6;
}

message Jurisdiction {
    optional string name = 1;
}

message RegulatorDetails {
    optional Regulator regulator = 1;
    enum RegulatorStatus {
        UNKNOWN = 0;
        ACTIVE = 1;
        SUSPENDED = 2;
    }
    optional RegulatorStatus status = 2 [default = UNKNOWN];
}

message Rule4Deduction {
    optional string id = 1;
    optional double deduction = 2;
    optional double placeDeduction = 3;
    optional bool isValid = 4;
    optional PriceType priceType = 5 [default = UNKNOWN];
    optional int64 timeFrom = 6;
    optional int64 timeTo = 7;
    optional string comment = 8;
    optional EntityId selectionId = 9;
    optional string version = 10;
    enum PriceType {
        UNKNOWN = 0;
        LIVE_PRICE = 1;
        STARTING_PRICE = 2;
    }
}

message MarketGroup {
    optional int64 marketGroupId = 1;
    optional string marketGroupName = 2;
    enum MarketGroupType {
        UNKNOWN = 0;
        REGULATOR = 1;
        OPERATOR = 2;
    }
    optional MarketGroupType marketGroupType = 3 [default = UNKNOWN];
    optional string regulatorKey = 4;
}

message Regulator {
    required string regulatorKey = 1;
    required string regulatorName = 2;
}


message MarketState {
    required Status status = 1 [default = UNKNOWN];
    optional int32 betDelay = 2;
    optional bool inplay = 5;
    optional int64 lastUpdateTime = 6;

    optional bool bettingAvailable = 8;
    optional int32 activeRunners = 11; // aka voidableRunners from EMS?
    optional bool hidden = 18 [default = true];
}

enum Status {
    UNKNOWN = 0;
    INACTIVE = 1;
    OPEN = 2;
    SUSPENDED = 3;
    CLOSED = 4;
}

message Runner {
    required EntityId runnerId = 1;
    optional string runnerName = 2;
    enum RunnerStatus {
        UNKNOWN = 0;
        ACTIVE = 1;
        SUSPENDED = 2;
        WINNER = 3;
        LOSER = 4;
        REMOVED = 5;
        VOIDED = 6; // Depending on the logic in mediation for now this may map to the same as REMOVED.
    }
    optional RunnerStatus status = 4 [default = UNKNOWN];
    optional int32 sortOrder = 5; //non zero based
    optional double handicap = 6;
    optional bool hidden = 8 [default = true];
    optional RunnerResult result = 9;
    optional int32 place = 10;
    optional bool resultConfirmed = 11 [default = false];
    enum RunnerType {
        NORMAL = 0;
        UNNAMED_2ND_FAVOURITE = 1;
        UNNAMED_FAVOURITE = 2;
    }
    optional RunnerType type = 12 [default = NORMAL];
    repeated RunnerJurisdictionDetails runnerJurisdictionDetails = 13;
    optional bool settled = 14 [default = false];
    optional string rampId = 15 [deprecated=true];
    repeated DeadHeatDeduction deadHeatDeductions = 16;
    optional int32 runnerNumber = 17;
    optional int32 minAccumulators = 18;
    optional string multipleKey = 19;
}

message RunnerJurisdictionDetails {
    optional Jurisdiction jurisdiction = 1;
    optional Result runnerResult = 2 [default = NONE];
    optional bool resultConfirmed = 3;
    optional bool settled = 4;
}

message RunnerResult {
    enum ResultType {
        UNKNOWN = 0;
        HOME = 1;
        AWAY = 2;
        DRAW = 3;
        OVER = 4;
        UNDER = 5;
        LINE = 6;
        SCORE = 7;
        NO_GOAL = 8;
        HOME_HOME = 9;
        HOME_DRAW = 10;
        HOME_AWAY = 11;
        DRAW_HOME = 12;
        DRAW_DRAW = 13;
        DRAW_AWAY = 14;
        AWAY_HOME = 15;
        AWAY_DRAW = 16;
        AWAY_AWAY = 17;
    }
    optional ResultType resultType = 1 [default = UNKNOWN];
    optional int32 scoreHome = 2;
    optional int32 scoreAway = 3;
    optional Result result = 4 [default = NONE];
}

enum Result {
    NONE = 0;
    HANDICAP = 1;
    PLACE = 2;
    WIN = 3;
    LOSE = 4;
    VOID = 5;
}

message Price {
    required EntityId runnerId = 1;
    optional string winFractionalOdds = 2;
    optional double winDecimalOdds = 3;
    repeated string previousWinFractionalOdds = 4;
    repeated double previousWinDecimalOdds = 5;
    optional string eachWayFractionalOdds = 6;
    optional double eachWayDecimalOdds = 7;
}

message RunnerProbability {
    required EntityId runnerId = 1;
    optional double probability = 2;
    repeated double previousProbabilities = 3;
}

message StartingPrice {
    required EntityId runnerId = 1;
    optional string startingFractionalOdds = 2;
    optional double startingDecimalOdds = 3;
}

message DeadHeatDeduction {
    enum DeadHeatDeductionType {
        UNKNOWN = 0;
        WIN = 1;
        EACH_WAY = 2;
    }
    optional string id = 1;
    optional double decimalDeduction = 2;
    optional string deduction = 3;
    optional DeadHeatDeductionType deductionType = 4 [default = UNKNOWN];
    optional int32 places = 5;
    optional string eachWayFraction = 6;
}

message MarketType {
    optional string name = 1;
    optional EntityId id = 2;
}

message MarketLinkDetails {
    enum MarketLinkType {
        DEFAULT = 0;
        ALTERNATIVE_TOTALS = 1;
        ALTERNATIVE_HANDICAP = 2;
        HR_INPLAY_ONLY = 3;
        LOTTERIES_BONUS_BALL = 4;
        PITCHER_HOME = 5;
        PITCHER_AWAY = 6;
        PITCHER_BOTH = 7;
    }

    required string marketId = 1;
    required MarketLinkType type = 2;
}

message Mappings {
    oneof field {
        SpainMapping spain_mapping = 1;
    }
}

message SpainMapping {
    optional string dgoj_sport_code = 1; // MVP
    optional string dgoj_sport_name = 2; // MVP
    optional string dgoj_event_type = 3; // MVP
    optional string dgoj_competition_country_iso = 4;
    optional string dgoj_competition_gender = 5;
    optional string dgoj_competition_category = 6;
    optional EventStatus event_status = 7 [default = EVENT_STATUS_UNSPECIFIED]; // MVP
    optional Timestamp event_end_date = 8; // MVP
    optional string dgoj_special_event = 9;
    optional string multi_event_leg = 10;
    optional string multi_competition_leg = 11; // MVP
}

enum EventStatus {
    EVENT_STATUS_UNSPECIFIED = 0;
    EVENT_STATUS_PRE_EVENT = 1;
    EVENT_STATUS_IN_PROGRESS = 2;
    EVENT_STATUS_POSTPONED = 3;
    EVENT_STATUS_SUSPENDED = 4;
    EVENT_STATUS_CANCELLED = 5;
    EVENT_STATUS_COMPLETED = 6;
}

message Timestamp {
    // Represents seconds of UTC time since Unix epoch
    // 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to
    // 9999-12-31T23:59:59Z inclusive.
    optional int64 seconds = 1;

    // Non-negative fractions of a second at nanosecond resolution. Negative
    // second values with fractions must still have non-negative nanos values
    // that count forward in time. Must be from 0 to 999,999,999
    // inclusive.
    optional int32 nanos = 2;
}