syntax = "proto2";

import "common.proto";

option java_package = "com.isp.platform.mas.messages";
option java_outer_classname = "MarketChangesProtocolBuffer";

message MarketChange {
    required EntityId marketId = 1;
    optional string partitionId= 2;
    optional int64 batchId = 3;
    optional int64 streamOffset = 4;
    optional int64 publishTime = 6;
    optional int64 creationTime = 7;
    enum ChangeType {
        UNKNOWN = 0;
        UPDATE = 1;//delta
        REPLACE = 2;//whole model
    }
    required ChangeType type = 8 [ default = UNKNOWN ];
    optional MarketDefinition marketDefinition = 9;
    repeated Price prices = 10;
    repeated RunnerProbability runnerProbabilities = 12;
    repeated StartingPrice startingPrices = 13;
}

message MarketChanges {
    required EntityId eventId = 1;
    optional int64 batchId = 2;
    optional int64 publishTime = 3;
    optional int64 inceptionTime = 4;
    optional int64 streamVersion = 5;
    optional bool reset = 6;
    repeated MarketChange marketChanges = 10;
}