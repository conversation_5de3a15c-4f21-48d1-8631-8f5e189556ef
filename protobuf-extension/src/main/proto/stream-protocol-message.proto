syntax = "proto2";

option java_package = "com.isp.platform.mas.proto";
option java_outer_classname = "StreamProtocolMessage";

message ProtocolHeaders {
    map<string, string> headers = 1;
}

message ApplicationHeaders {
    map<string, string> headers = 1;
}

message StreamProtocolEnvelope {
    optional ProtocolHeaders protocolHeaders = 1;
    optional ApplicationHeaders applicationHeaders = 2;
    optional bytes payload = 3;
}

