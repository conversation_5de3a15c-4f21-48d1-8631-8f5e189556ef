<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="MarketAggregatorConsumerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
    <module name="market-aggregator-consumer" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.flutter.consumer.MarketAggregatorConsumerApplication" />
    <option name="VM_PARAMETERS" value="--illegal-access=debug --add-opens=java.base/java.nio=ALL-UNNAMED" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>