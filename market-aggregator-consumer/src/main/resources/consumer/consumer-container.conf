akka {
  loggers = [akka.event.slf4j.Slf4jLogger]
  log-dead-letters = on
  log-dead-letters-during-shutdown = true
  loglevel = DEBUG
  # Log the complete configuration at INFO level when the actor system is started.
  # This is useful when you are uncertain of what configuration is used.
  log-config-on-start = off
  actor {
    debug {
      lifecycle = on
      unhandled = on
      receive = on
      # enable DEBUG logging of all AutoReceiveMessages (Kill, PoisonPill et.c.)
      autoreceive = on
      # enable DEBUG logging of subscription changes on the eventStream
      event-stream = on
    }
  }

  log-dead-letters = on
}

// Consumer container specific configuration
consumer-container {
  bootstrap {
    init-timeout = 10 seconds
  }
  shutdown {
    termination-timeout = 15 seconds
  }
  consumer {
    reactive = false
    reactive-consumer {
      parallelism = 10
      batch = 20
      timeout = 50 seconds
      on-failure-strategy = "com.ppb.platform.stream.consumer.core.consumer.reactive.strategy.ThrowStrategy"
      min-restart-backoff = "1 seconds"
      max-restart-backoff = "30 seconds"
      random-restart-factor = "0.2"
      max-restarts = 0
    }
    eviction-manager {
      enabled = false
    }
    partition-consumer {
      shard.number = 1
      timeout = 2 seconds
    }
  }

  tracing {
    name = "IT-"${consumer-container.name}
    jaeger {
      hostname = "127.0.0.1"
      port = 6831
      samplerType = "remote"
      samplerParam = 1
      logSpans = false
    }
  }
}

jmx {
  base.name = "com.ppb.platform.stream.protocol.consumerContainer"
}

metrics {
  enabled = false
}
