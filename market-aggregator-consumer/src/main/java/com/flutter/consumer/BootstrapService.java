package com.flutter.consumer;

import com.betfair.platform.fms.model.MarketChange;
import com.betfair.platform.fms.model.MarketChanges;
import com.betfair.platform.fms.model.MarketView;
import com.flutter.consumer.config.sp.MarketStreamClientWrapper;
import com.flutter.consumer.mappings.updates.MappingUpdate;
import com.flutter.consumer.mappings.view.MappingView;
import com.ppb.platform.stream.consumer.ConsumerContainer;
import com.ppb.platform.stream.container.ContainerStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class BootstrapService {

//    private final ConsumerContainer<MarketView, MarketChanges, MarketChange> consumerContainer;
    private final MarketStreamClientWrapper marketStreamClientWrapper;

    @EventListener(ApplicationReadyEvent.class)
    public void handleApplicationReadyEvent() {
        log.info("handleApplicationReadyEvent: Starting Consumer Container...");
//        marketStreamClientWrapper.start();
//        ContainerStatus start = consumerContainer.start();
//        log.info("handleApplicationReadyEvent: ContainerStatus({})", start);
    }
}
