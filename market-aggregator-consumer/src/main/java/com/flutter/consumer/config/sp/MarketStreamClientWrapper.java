package com.flutter.consumer.config.sp;

import com.ppb.platform.sb.fmg.MarketStreamClient;

public class MarketStreamClientWrapper {

    private final MarketStreamClient marketStreamClient;

    public MarketStreamClientWrapper(MarketStreamClient marketStreamClient) {
        this.marketStreamClient = marketStreamClient;
    }

    public void start() {
        // Delegate or augment behavior
        marketStreamClient.start();
    }

    public void stop() {
        // Delegate or augment behavior
        marketStreamClient.stop();
    }

    // You can add more methods if you want to expose additional functionality
    public MarketStreamClient getDelegate() {
        return marketStreamClient;
    }

}