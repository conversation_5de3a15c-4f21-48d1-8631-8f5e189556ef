package com.flutter.consumer.config.sp;

import com.betfair.platform.fms.MarketChangeListener;
import com.betfair.platform.fms.model.MarketChanges;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CustomMarketChangeListener implements MarketChangeListener {
    @Override
    public void onMarketChanges(int i, long l, MarketChanges marketChanges) {
      log.info("onMarketChanges: {}", marketChanges);
    }
}
