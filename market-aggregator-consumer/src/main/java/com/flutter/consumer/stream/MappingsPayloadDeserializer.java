package com.flutter.consumer.stream;


import com.betfair.platform.fms.model.MarketChanges;
import com.betfair.platform.fms.serializers.JournalingSerializer;
import com.betfair.platform.fms.serializers.protobuffer.MarketChangesSerDeser;
import com.ppb.platform.stream.serialization.PayloadDeserializer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MappingsPayloadDeserializer implements PayloadDeserializer<MarketChanges> {

    private final MarketChangesSerDeser marketChangesSerDeser = new MarketChangesSerDeser();
    private final JournalingSerializer serializer = new JournalingSerializer(marketChangesSerDeser);

    @Override
    @SneakyThrows
    public MarketChanges deserialize(byte[] from) {

        return serializer.deserialize(from);
    }
}
