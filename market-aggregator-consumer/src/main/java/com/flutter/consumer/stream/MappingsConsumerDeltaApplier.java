package com.flutter.consumer.stream;


import com.betfair.platform.fms.model.MarketChange;
import com.betfair.platform.fms.model.MarketChanges;
import com.betfair.platform.fms.model.MarketView;
import com.flutter.consumer.mappings.updates.SpainMappingUpdate;
import com.flutter.consumer.mappings.view.MappingView;
import com.flutter.consumer.mappings.view.SpainMappingView;
import com.ppb.platform.stream.consumer.core.deltaapplier.ConsumerData;
import com.ppb.platform.stream.consumer.core.deltaapplier.ConsumerDeltaApplier;
import com.ppb.platform.stream.container.metrics.GlobalContextKeys;
import com.ppb.platform.stream.model.context.ContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import scala.Option;
import scala.collection.JavaConversions;
import scala.collection.immutable.List;

import java.util.stream.Collectors;

@Component
@Slf4j
public class MappingsConsumerDeltaApplier implements ConsumerDeltaApplier<MarketView, MarketChanges, MarketChange> {

    @Override
    public List<ConsumerData<MarketChange>> deMux(MarketChanges delta) {
        log.info(delta.toString());

        return JavaConversions.asScalaBuffer(
                        delta.getMarketChanges()
                                .stream()
                                .map(ms -> new ConsumerData<>(ms.getMarketId(), ms))
                                .collect(Collectors.toList()))
                .toList();
    }

    @Override
    public Option<MarketView> apply(
            ContextHolder protocolCtx,
            Option<ContextHolder> appCtx,
            Option<MarketView> currentView,
            ConsumerData<MarketChange> delta
    ) {
        String instructionType = protocolCtx.getContextValue(GlobalContextKeys.INSTRUCTION_TYPE()).get();
        String instructionKafkaTopicOffset = protocolCtx.getContextValue(GlobalContextKeys.INSTRUCTION_KAFKA_TOPIC_OFFSET()).get();

//        MappingView newMappingView = mergeUpdate(currentView, null);
//        log.info("apply:\n\tinstructionType: {}\n\tinstructionKafkaTopicOffset: {}\n\tCurrentView: {}\n\tUpdate: {}\n\tNewView: {}",
//                instructionType, instructionKafkaTopicOffset, currentView, delta, newMappingView);

        if (currentView.isEmpty()) {
            MarketView marketView = new MarketView(delta.data().getId());
            return Option.apply(marketView);
        }

        return Option.apply(new MarketView(delta.data().getId()));
    }

//    private static MarketView mergeUpdate(Option<MappingView> base, ConsumerData<MarketChanges> update) {
//        MappingView.MappingViewBuilder newViewBuilder = MappingView.builder();
//
//        SpainMappingView currentSpainMappingView = null;
//        if (base.isDefined()) {
//            MappingView currentView = base.get();
//            currentSpainMappingView = currentView.getSpainMappingView();
//        }
//
//        SpainMappingView newSpainMappingView =
//                mergeSpainUpdate(currentSpainMappingView, update.data().getSpainMappingUpdate());
//        newViewBuilder.spainMappingView(newSpainMappingView);
//
//        return newViewBuilder.build();
//    }

    private static SpainMappingView mergeSpainUpdate(SpainMappingView currentView, SpainMappingUpdate update) {
        SpainMappingView.SpainMappingViewBuilder builder = SpainMappingView.builder();
        if (currentView != null) {
            builder.dgojSportCode(currentView.getDgojSportCode())
                    .dgojSportName(currentView.getDgojSportName())
                    .dgojEventType(currentView.getDgojEventType())
                    .dgojCompetitionCountryIso(currentView.getDgojCompetitionCountryIso())
                    .dgojCompetitionGender(currentView.getDgojCompetitionGender())
                    .dgojCompetitionCategory(currentView.getDgojCompetitionCategory())
                    .eventStatus(currentView.getEventStatus())
                    .eventEndDate(currentView.getEventEndDate())
                    .dgojSpecialEvent(currentView.getDgojSpecialEvent())
                    .multiEventLeg(currentView.getMultiEventLeg())
                    .multiCompetitionLeg(currentView.getMultiCompetitionLeg());
        }
        // override with the latest update
        if (update != null) {
            builder.dgojSportCode(update.getDgojSportCode())
                    .dgojSportName(update.getDgojSportName())
                    .dgojEventType(update.getDgojEventType())
                    .dgojCompetitionCountryIso(update.getDgojCompetitionCountryIso())
                    .dgojCompetitionGender(update.getDgojCompetitionGender())
                    .dgojCompetitionCategory(update.getDgojCompetitionCategory())
                    .eventStatus(toEventStatus(update.getEventStatus()))
                    .eventEndDate(update.getEventEndDate())
                    .dgojSpecialEvent(update.getDgojSpecialEvent())
                    .multiEventLeg(update.getMultiEventLeg())
                    .multiCompetitionLeg(update.getMultiCompetitionLeg());
        }
        return builder.build();
    }

    public static SpainMappingView.EventStatus toEventStatus(SpainMappingUpdate.EventStatus eventStatus) {
        switch (eventStatus) {
            case UNSPECIFIED:
                return SpainMappingView.EventStatus.UNSPECIFIED;
            case PRE_EVENT:
                return SpainMappingView.EventStatus.PRE_EVENT;
            case IN_PROGRESS:
                return SpainMappingView.EventStatus.IN_PROGRESS;
            case POSTPONED:
                return SpainMappingView.EventStatus.POSTPONED;
            case SUSPENDED:
                return SpainMappingView.EventStatus.SUSPENDED;
            case CANCELLED:
                return SpainMappingView.EventStatus.CANCELLED;
            case COMPLETED:
                return SpainMappingView.EventStatus.COMPLETED;
            default:
                return null;
        }
    }
}
