package com.flutter.consumer.factory;

import com.flutter.consumer.config.props.ConsumerProperties;
import com.flutter.consumer.mappings.view.MappingView;
import com.ppb.platform.stream.consumer.ConsumerContainer;
import com.ppb.platform.stream.consumer.core.consumer.validation.NoOpValidator;
import com.ppb.platform.stream.consumer.core.datasource.StreamConsumerDatasource;
import com.ppb.platform.stream.consumer.core.deltaapplier.ConsumerDeltaApplier;
import com.ppb.platform.stream.consumer.core.indexing.IndexFunction;
import com.ppb.platform.stream.consumer.core.notifier.StreamNotificationObserver;
import com.ppb.platform.stream.serialization.PayloadDeserializer;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import scala.Option;
import scala.collection.immutable.List;
import scala.collection.immutable.Set;

import java.util.Random;

import static com.typesafe.config.ConfigValueFactory.fromAnyRef;

public class ConsumerContainerFactory {

    public static <MV, M, MU> ConsumerContainer<MV, M, MU> newInstance(
            ConsumerProperties consumerProperties,
            ConsumerDeltaApplier<MV, M, MU> consumerDeltaApplier,
            PayloadDeserializer<M> payloadDeserializer,
            List<StreamNotificationObserver<MV, MU>> observerList,
            StreamConsumerDatasource<MV> consumerDatasource,
            Option<Config> consumerOverrideConfigs,
            Set<IndexFunction<MV>> indexFunctions
    ) {
        Config consumerContainerConfig = baseContainerConsumerConfig(consumerProperties);
        if (consumerOverrideConfigs.isDefined()) {
            consumerContainerConfig = consumerOverrideConfigs.get().withFallback(consumerContainerConfig);
        }

        return new ConsumerContainer<>(
                consumerConfig(consumerProperties),
                Option.apply(consumerContainerConfig),
                consumerDeltaApplier,
                payloadDeserializer,
                observerList,
                consumerDatasource,
                new NoOpValidator<>(),
                indexFunctions);
    }

    private static Config baseContainerConsumerConfig(ConsumerProperties consumerProperties) {
        String topicName = consumerProperties.getTopic();
        String zkHost = consumerProperties.getZookeeperServers();
        int httpPort = consumerProperties.getAkkaHttpPort();
        return ConfigFactory.parseResources("consumer/consumer-container.conf")
                .withValue("consumer-container.bootstrap.metadata-consumer.zk-endpoints", fromAnyRef(zkHost))
                .withValue("consumer-container.bootstrap.metadata-consumer.zNodePath", fromAnyRef("/" + topicName))
                .withValue("consumer-container.bootstrap.http.port", fromAnyRef(httpPort));
    }

    private static Config consumerConfig(ConsumerProperties consumerProperties) {
        String topicName = consumerProperties.getTopic();
        String kafkaHost = consumerProperties.getBootstrapServers();
        return ConfigFactory.parseResources("consumer/consumer.conf")
                .withValue("consumer.bootstrap.servers", fromAnyRef(kafkaHost))
                .withValue("consumer.auto.offset.reset", fromAnyRef("earliest"))
                .withValue("consumer.group.id", fromAnyRef("test-group-id-" + new Random().nextInt()))
                .withValue("consumer.topic", fromAnyRef(topicName));
    }
}
