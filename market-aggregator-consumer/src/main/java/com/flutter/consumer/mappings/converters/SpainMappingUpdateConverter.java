//package com.flutter.consumer.mappings.converters;
//
//import com.flutter.consumer.mappings.updates.SpainMappingUpdate;
//import com.isp.platform.mas.messages.Common;
//
//import java.time.Instant;
//
//public class SpainMappingUpdateConverter {
//
//
//    public static SpainMappingUpdate convertFrom(Common.SpainMapping spainMapping) {
//        return SpainMappingUpdate.builder()
//                .dgojSportCode(spainMapping.getDgojSportCode())
//                .dgojSportName(spainMapping.getDgojSportName())
//                .dgojEventType(spainMapping.getDgojEventType())
//                .dgojCompetitionCountryIso(spainMapping.getDgojCompetitionCountryIso())
//                .dgojCompetitionGender(spainMapping.getDgojCompetitionGender())
//                .dgojCompetitionCategory(spainMapping.getDgojCompetitionCategory())
//                .eventStatus(convertFrom(spainMapping.getEventStatus()))
//                .eventEndDate(covertFrom(spainMapping.getEventEndDate()))
//                .multiEventLeg(spainMapping.getMultiEventLeg())
//                .multiCompetitionLeg(spainMapping.getMultiCompetitionLeg())
//                .build();
//    }
//
//    public static SpainMappingUpdate.EventStatus convertFrom(Common.EventStatus eventStatus) {
//        switch (eventStatus) {
//            case EVENT_STATUS_UNSPECIFIED: return SpainMappingUpdate.EventStatus.UNSPECIFIED;
//            case EVENT_STATUS_PRE_EVENT: return SpainMappingUpdate.EventStatus.PRE_EVENT;
//            case EVENT_STATUS_IN_PROGRESS: return SpainMappingUpdate.EventStatus.IN_PROGRESS;
//            case EVENT_STATUS_POSTPONED: return SpainMappingUpdate.EventStatus.POSTPONED;
//            case EVENT_STATUS_SUSPENDED: return SpainMappingUpdate.EventStatus.SUSPENDED;
//            case EVENT_STATUS_CANCELLED: return SpainMappingUpdate.EventStatus.CANCELLED;
//            case EVENT_STATUS_COMPLETED: return SpainMappingUpdate.EventStatus.COMPLETED;
//            default: return null;
//        }
//    }
//
//    public static Instant covertFrom(Common.Timestamp timestamp) {
//        return Instant.ofEpochSecond(timestamp.getSeconds(), timestamp.getNanos());
//    }
//}
