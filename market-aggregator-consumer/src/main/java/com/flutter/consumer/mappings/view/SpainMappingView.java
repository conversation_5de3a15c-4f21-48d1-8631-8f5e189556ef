package com.flutter.consumer.mappings.view;

import com.flutter.consumer.mappings.updates.SpainMappingUpdate;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;

@Data
@Builder
public class SpainMappingView {

    public enum EventStatus {
        UNSPECIFIED,
        PRE_EVENT,
        IN_PROGRESS,
        POSTPONED,
        SUSPENDED,
        CANCELLED,
        COMPLETED
    }

    private final String dgojSportCode;
    private final String dgojSportName;
    private final String dgojEventType;
    private final String dgojCompetitionCountryIso;
    private final String dgojCompetitionGender;
    private final String dgojCompetitionCategory;
    private final EventStatus eventStatus;
    private final Instant eventEndDate;
    private final String dgojSpecialEvent;
    private final String multiEventLeg;
    private final String multiCompetitionLeg;
}
