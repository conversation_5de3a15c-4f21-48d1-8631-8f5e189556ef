[INFO] Scanning for projects...
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.pom
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.pom
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.pom
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.pom
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.pom
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.pom
Progress (1): 1.4/7.4 kB
Progress (1): 2.8/7.4 kB
Progress (1): 4.1/7.4 kB
Progress (1): 5.5/7.4 kB
Progress (1): 6.9/7.4 kB
Progress (1): 7.4 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.pom (7.4 kB at 26 kB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/codehaus/mojo/mojo-parent/65/mojo-parent-65.pom
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/codehaus/mojo/mojo-parent/65/mojo-parent-65.pom
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/codehaus/mojo/mojo-parent/65/mojo-parent-65.pom
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/codehaus/mojo/mojo-parent/65/mojo-parent-65.pom
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/codehaus/mojo/mojo-parent/65/mojo-parent-65.pom
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/mojo-parent/65/mojo-parent-65.pom
Progress (1): 1.4/35 kB
Progress (1): 2.8/35 kB
Progress (1): 4.1/35 kB
Progress (1): 5.5/35 kB
Progress (1): 6.9/35 kB
Progress (1): 8.3/35 kB
Progress (1): 9.7/35 kB
Progress (1): 11/35 kB 
Progress (1): 12/35 kB
Progress (1): 14/35 kB
Progress (1): 15/35 kB
Progress (1): 16/35 kB
Progress (1): 17/35 kB
Progress (1): 19/35 kB
Progress (1): 20/35 kB
Progress (1): 21/35 kB
Progress (1): 23/35 kB
Progress (1): 24/35 kB
Progress (1): 25/35 kB
Progress (1): 27/35 kB
Progress (1): 28/35 kB
Progress (1): 30/35 kB
Progress (1): 31/35 kB
Progress (1): 32/35 kB
Progress (1): 34/35 kB
Progress (1): 35 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/mojo-parent/65/mojo-parent-65.pom (35 kB at 631 kB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.jar
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.jar
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.jar
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.jar
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.jar
Progress (1): 16/68 kB
Progress (1): 33/68 kB
Progress (1): 49/68 kB
Progress (1): 66/68 kB
Progress (1): 68 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/codehaus/mojo/build-helper-maven-plugin/3.3.0/build-helper-maven-plugin-3.3.0.jar (68 kB at 67 B/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.pom
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.pom
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.pom
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.pom
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.pom
Downloading from central: https://repo.maven.apache.org/maven2/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.pom
Progress (1): 4.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.pom (4.7 kB at 179 kB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.jar
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.jar
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.jar
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.jar
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.jar
Progress (1): 16/130 kB
Progress (1): 33/130 kB
Progress (1): 49/130 kB
Progress (1): 62/130 kB
Progress (1): 79/130 kB
Progress (1): 95/130 kB
Progress (1): 111/130 kB
Progress (1): 125/130 kB
Progress (1): 130 kB    
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/flywaydb/flyway-maven-plugin/8.5.13/flyway-maven-plugin-8.5.13.jar (130 kB at 2.9 MB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.pom
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.pom
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.pom
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.pom
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.pom
Downloading from central: https://repo.maven.apache.org/maven2/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.pom
Progress (1): 12 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.pom (12 kB at 800 kB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/pl/project13/maven/git-commit-id-plugin-parent/4.9.10/git-commit-id-plugin-parent-4.9.10.pom
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/pl/project13/maven/git-commit-id-plugin-parent/4.9.10/git-commit-id-plugin-parent-4.9.10.pom
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/pl/project13/maven/git-commit-id-plugin-parent/4.9.10/git-commit-id-plugin-parent-4.9.10.pom
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/pl/project13/maven/git-commit-id-plugin-parent/4.9.10/git-commit-id-plugin-parent-4.9.10.pom
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/pl/project13/maven/git-commit-id-plugin-parent/4.9.10/git-commit-id-plugin-parent-4.9.10.pom
Downloading from central: https://repo.maven.apache.org/maven2/pl/project13/maven/git-commit-id-plugin-parent/4.9.10/git-commit-id-plugin-parent-4.9.10.pom
Progress (1): 12 kB
                   
Downloaded from central: https://repo.maven.apache.org/maven2/pl/project13/maven/git-commit-id-plugin-parent/4.9.10/git-commit-id-plugin-parent-4.9.10.pom (12 kB at 769 kB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.jar
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.jar
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.jar
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.jar
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.jar
Downloading from central: https://repo.maven.apache.org/maven2/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.jar
Progress (1): 16/40 kB
Progress (1): 33/40 kB
Progress (1): 40 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/pl/project13/maven/git-commit-id-plugin/4.9.10/git-commit-id-plugin-4.9.10.jar (40 kB at 2.1 MB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/apache/johnzon/johnzon-maven-plugin/1.2.19/johnzon-maven-plugin-1.2.19.pom
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/apache/johnzon/johnzon-maven-plugin/1.2.19/johnzon-maven-plugin-1.2.19.pom
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/apache/johnzon/johnzon-maven-plugin/1.2.19/johnzon-maven-plugin-1.2.19.pom
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/apache/johnzon/johnzon-maven-plugin/1.2.19/johnzon-maven-plugin-1.2.19.pom
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/apache/johnzon/johnzon-maven-plugin/1.2.19/johnzon-maven-plugin-1.2.19.pom
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/johnzon/johnzon-maven-plugin/1.2.19/johnzon-maven-plugin-1.2.19.pom
Progress (1): 4.0 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/johnzon/johnzon-maven-plugin/1.2.19/johnzon-maven-plugin-1.2.19.pom (4.0 kB at 118 kB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/apache/johnzon/johnzon/1.2.19/johnzon-1.2.19.pom
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/apache/johnzon/johnzon/1.2.19/johnzon-1.2.19.pom
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/apache/johnzon/johnzon/1.2.19/johnzon-1.2.19.pom
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/apache/johnzon/johnzon/1.2.19/johnzon-1.2.19.pom
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/apache/johnzon/johnzon/1.2.19/johnzon-1.2.19.pom
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/johnzon/johnzon/1.2.19/johnzon-1.2.19.pom
Progress (1): 16/29 kB
Progress (1): 29 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/johnzon/johnzon/1.2.19/johnzon-1.2.19.pom (29 kB at 1.4 MB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/apache/johnzon/johnzon-maven-plugin/1.2.19/johnzon-maven-plugin-1.2.19.jar
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/apache/johnzon/johnzon-maven-plugin/1.2.19/johnzon-maven-plugin-1.2.19.jar
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/apache/johnzon/johnzon-maven-plugin/1.2.19/johnzon-maven-plugin-1.2.19.jar
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/apache/johnzon/johnzon-maven-plugin/1.2.19/johnzon-maven-plugin-1.2.19.jar
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/apache/johnzon/johnzon-maven-plugin/1.2.19/johnzon-maven-plugin-1.2.19.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/apache/johnzon/johnzon-maven-plugin/1.2.19/johnzon-maven-plugin-1.2.19.jar
Progress (1): 16/36 kB
Progress (1): 33/36 kB
Progress (1): 36 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/apache/johnzon/johnzon-maven-plugin/1.2.19/johnzon-maven-plugin-1.2.19.jar (36 kB at 2.2 MB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.pom
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.pom
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.pom
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.pom
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.pom
Downloading from central: https://repo.maven.apache.org/maven2/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.pom
Progress (1): 4.1 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.pom (4.1 kB at 274 kB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/jooq/jooq-parent/3.14.16/jooq-parent-3.14.16.pom
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/jooq/jooq-parent/3.14.16/jooq-parent-3.14.16.pom
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/jooq/jooq-parent/3.14.16/jooq-parent-3.14.16.pom
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/jooq/jooq-parent/3.14.16/jooq-parent-3.14.16.pom
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/jooq/jooq-parent/3.14.16/jooq-parent-3.14.16.pom
Downloading from central: https://repo.maven.apache.org/maven2/org/jooq/jooq-parent/3.14.16/jooq-parent-3.14.16.pom
Progress (1): 16/28 kB
Progress (1): 28 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/jooq/jooq-parent/3.14.16/jooq-parent-3.14.16.pom (28 kB at 1.6 MB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.jar
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.jar
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.jar
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.jar
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.jar
Progress (1): 16/17 kB
Progress (1): 17 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/jooq/jooq-codegen-maven/3.14.16/jooq-codegen-maven-3.14.16.jar (17 kB at 793 kB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.pom
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.pom
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.pom
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.pom
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.pom
Downloading from central: https://repo.maven.apache.org/maven2/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.pom
Progress (1): 5.9 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.pom (5.9 kB at 532 kB/s)
[INFO] Artifact org.jetbrains.kotlin:kotlin-project:pom:1.6.21 is present in the local repository, but cached from a remote repository ID that is unavailable in current build context, verifying that is downloadable from [plugin-artifactory-central (https://artifactory-prd.prd.betfair/artifactory/libs-release, default, releases), plugin-artifactory-snapshots (https://artifactory-prd.prd.betfair/artifactory/libs-snapshot, default, releases+snapshots), artifactory-libs-release (https://artifactory.prod.isp.starsops.com/artifactory/libs-release, default, releases), artifactory-libs-release-cache (https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache, default, releases), artifactory-libs-snapshot (https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot, default, releases), central (https://repo.maven.apache.org/maven2, default, releases)]
[INFO] Artifact org.jetbrains.kotlin:kotlin-project:pom:1.6.21 is present in the local repository, but cached from a remote repository ID that is unavailable in current build context, verifying that is downloadable from [plugin-artifactory-central (https://artifactory-prd.prd.betfair/artifactory/libs-release, default, releases), plugin-artifactory-snapshots (https://artifactory-prd.prd.betfair/artifactory/libs-snapshot, default, releases+snapshots), artifactory-libs-release (https://artifactory.prod.isp.starsops.com/artifactory/libs-release, default, releases), artifactory-libs-release-cache (https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache, default, releases), artifactory-libs-snapshot (https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot, default, releases), central (https://repo.maven.apache.org/maven2, default, releases)]
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/jetbrains/kotlin/kotlin-project/1.6.21/kotlin-project-1.6.21.pom
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/jetbrains/kotlin/kotlin-project/1.6.21/kotlin-project-1.6.21.pom
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/jetbrains/kotlin/kotlin-project/1.6.21/kotlin-project-1.6.21.pom
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/jetbrains/kotlin/kotlin-project/1.6.21/kotlin-project-1.6.21.pom
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/jetbrains/kotlin/kotlin-project/1.6.21/kotlin-project-1.6.21.pom
Downloading from central: https://repo.maven.apache.org/maven2/org/jetbrains/kotlin/kotlin-project/1.6.21/kotlin-project-1.6.21.pom
Downloaded from central: https://repo.maven.apache.org/maven2/org/jetbrains/kotlin/kotlin-project/1.6.21/kotlin-project-1.6.21.pom (0 B at 0 B/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.jar
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.jar
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.jar
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.jar
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.jar
Progress (1): 16/80 kB
Progress (1): 33/80 kB
Progress (1): 49/80 kB
Progress (1): 64/80 kB
Progress (1): 80 kB   
                   
Downloaded from central: https://repo.maven.apache.org/maven2/org/jetbrains/kotlin/kotlin-maven-plugin/1.6.21/kotlin-maven-plugin-1.6.21.jar (80 kB at 4.7 MB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.pom
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.pom
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.pom
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.pom
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.pom
Downloading from central: https://repo.maven.apache.org/maven2/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.pom
Progress (1): 2.7 kB
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.pom (2.7 kB at 227 kB/s)
Downloading from plugin-artifactory-central: https://artifactory-prd.prd.betfair/artifactory/libs-release/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.jar
Downloading from plugin-artifactory-snapshots: https://artifactory-prd.prd.betfair/artifactory/libs-snapshot/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.jar
Downloading from artifactory-libs-release: https://artifactory.prod.isp.starsops.com/artifactory/libs-release/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.jar
Downloading from artifactory-libs-release-cache: https://artifactory.prod.isp.starsops.com/artifactory/libs-release-cache/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.jar
Downloading from artifactory-libs-snapshot: https://artifactory.prod.isp.starsops.com/artifactory/libs-snapshot/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.jar
Downloading from central: https://repo.maven.apache.org/maven2/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.jar
Progress (1): 16/108 kB
Progress (1): 33/108 kB
Progress (1): 49/108 kB
Progress (1): 64/108 kB
Progress (1): 80/108 kB
Progress (1): 96/108 kB
Progress (1): 108 kB   
                    
Downloaded from central: https://repo.maven.apache.org/maven2/org/liquibase/liquibase-maven-plugin/4.9.1/liquibase-maven-plugin-4.9.1.jar (108 kB at 5.4 MB/s)
[INFO] 
[INFO] ---------------< com.flutter:market-aggregator-consumer >---------------
[INFO] Building market-aggregator-consumer 0.0.1-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[WARNING] 2 problems were encountered while building the effective model for org.apache.yetus:audience-annotations:jar:0.5.0 during dependency collection step for project (use -X to see details)
[INFO] 
[INFO] --- dependency:3.3.0:tree (default-cli) @ market-aggregator-consumer ---
[INFO] com.flutter:market-aggregator-consumer:jar:0.0.1-SNAPSHOT
[INFO] +- org.springframework.boot:spring-boot-starter:jar:2.7.8:compile
[INFO] |  +- org.springframework.boot:spring-boot:jar:2.7.8:compile
[INFO] |  |  \- org.springframework:spring-context:jar:5.3.25:compile
[INFO] |  |     \- org.springframework:spring-expression:jar:5.3.25:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:2.7.8:compile
[INFO] |  +- jakarta.annotation:jakarta.annotation-api:jar:1.3.5:compile
[INFO] |  +- org.springframework:spring-core:jar:5.3.25:compile
[INFO] |  |  \- org.springframework:spring-jcl:jar:5.3.25:compile
[INFO] |  \- org.yaml:snakeyaml:jar:1.30:compile
[INFO] +- org.springframework.boot:spring-boot-starter-log4j2:jar:2.7.8:compile
[INFO] |  +- org.apache.logging.log4j:log4j-slf4j-impl:jar:2.17.2:compile
[INFO] |  |  \- org.apache.logging.log4j:log4j-api:jar:2.17.2:compile
[INFO] |  +- org.apache.logging.log4j:log4j-core:jar:2.17.2:compile
[INFO] |  +- org.apache.logging.log4j:log4j-jul:jar:2.17.2:compile
[INFO] |  \- org.slf4j:jul-to-slf4j:jar:1.7.36:compile
[INFO] +- org.springframework.boot:spring-boot-configuration-processor:jar:2.7.8:compile
[INFO] +- org.projectlombok:lombok:jar:1.18.30:compile
[INFO] +- com.ppb.platform.stream:stream-protocol-consumer:jar:4.1.0_3.3_2.11-SNAPSHOT:compile
[INFO] |  +- com.ppb.platform.stream:stream-protocol-common:jar:4.1.0_3.3_2.11-SNAPSHOT:compile
[INFO] |  |  +- com.ppb.platform.stream:stream-protocol-message:jar:4.1.0_3.3_2.11-SNAPSHOT:compile
[INFO] |  |  +- io.kamon:kamon-core_2.11:jar:1.1.5:compile
[INFO] |  |  |  \- com.lihaoyi:fansi_2.11:jar:0.2.4:compile
[INFO] |  |  |     \- com.lihaoyi:sourcecode_2.11:jar:0.1.3:compile
[INFO] |  |  +- io.kamon:kamon-akka-remote-2.5_2.11:jar:1.1.0:compile
[INFO] |  |  |  \- io.kamon:kamon-scala-future_2.11:jar:1.0.0:compile
[INFO] |  |  +- io.kamon:kamon-akka-2.5_2.11:jar:1.1.3:compile
[INFO] |  |  |  \- io.kamon:kamon-executors_2.11:jar:1.0.1:compile
[INFO] |  |  +- io.kamon:kamon-system-metrics_2.11:jar:1.0.1:compile
[INFO] |  |  +- com.ppb.platform.sb:kamon-jmx-reporter_2.11:jar:2.1:compile
[INFO] |  |  +- io.kamon:sigar-loader:jar:1.6.6-rev002:compile
[INFO] |  |  \- org.hdrhistogram:HdrHistogram:jar:2.1.9:compile
[INFO] |  +- com.ppb.platform.stream:stream-protocol-metadata:jar:4.1.0_3.3_2.11-SNAPSHOT:compile
[INFO] |  |  +- org.apache.curator:curator-recipes:jar:4.0.1:compile
[INFO] |  |  |  \- org.apache.curator:curator-framework:jar:4.0.1:compile
[INFO] |  |  |     \- org.apache.curator:curator-client:jar:4.0.1:compile
[INFO] |  |  \- com.softwaremill.retry:retry_2.11:jar:0.3.5:compile
[INFO] |  |     +- com.softwaremill.odelay:odelay-core_2.11:jar:0.3.3:compile
[INFO] |  |     \- org.scala-lang.modules:scala-collection-compat_2.11:jar:2.6.0:compile
[INFO] |  +- org.scala-lang:scala-library:jar:2.11.12:compile
[INFO] |  +- org.scala-lang:scala-reflect:jar:2.11.12:compile
[INFO] |  +- org.apache.kafka:kafka_2.11:jar:2.1.1:compile
[INFO] |  |  +- org.apache.kafka:kafka-clients:jar:3.1.2:compile
[INFO] |  |  |  +- com.github.luben:zstd-jni:jar:1.5.0-4:runtime
[INFO] |  |  |  +- org.lz4:lz4-java:jar:1.8.0:runtime
[INFO] |  |  |  \- org.xerial.snappy:snappy-java:jar:*******:runtime
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-databind:jar:********:compile
[INFO] |  |  |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.13.4:compile
[INFO] |  |  |  \- com.fasterxml.jackson.core:jackson-core:jar:2.13.4:compile
[INFO] |  |  +- net.sf.jopt-simple:jopt-simple:jar:5.0.4:compile
[INFO] |  |  +- com.yammer.metrics:metrics-core:jar:2.2.0:compile
[INFO] |  |  +- com.typesafe.scala-logging:scala-logging_2.11:jar:3.9.0:compile
[INFO] |  |  \- com.101tec:zkclient:jar:0.11:compile
[INFO] |  +- com.typesafe.akka:akka-actor_2.11:jar:2.5.25:compile
[INFO] |  +- com.typesafe.akka:akka-slf4j_2.11:jar:2.5.25:compile
[INFO] |  +- org.apache.zookeeper:zookeeper:jar:3.4.13:compile
[INFO] |  |  +- jline:jline:jar:0.9.94:compile
[INFO] |  |  +- org.apache.yetus:audience-annotations:jar:0.5.0:compile
[INFO] |  |  \- io.netty:netty:jar:3.10.6.Final:compile
[INFO] |  +- net.cakesolutions:scala-kafka-client_2.11:jar:2.1.0:compile
[INFO] |  +- com.typesafe.akka:akka-stream_2.11:jar:2.5.25:compile
[INFO] |  |  +- com.typesafe.akka:akka-protobuf_2.11:jar:2.5.25:compile
[INFO] |  |  +- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  \- com.typesafe:ssl-config-core_2.11:jar:0.3.8:compile
[INFO] |  |     \- org.scala-lang.modules:scala-parser-combinators_2.11:jar:1.1.1:compile
[INFO] |  +- net.cakesolutions:scala-kafka-client-akka_2.11:jar:2.1.0:compile
[INFO] |  +- com.iheart:ficus_2.11:jar:1.4.7:compile
[INFO] |  |  \- org.typelevel:macro-compat_2.11:jar:1.1.1:compile
[INFO] |  +- com.typesafe.akka:akka-http_2.11:jar:10.1.7:compile
[INFO] |  +- com.typesafe.akka:akka-http-core_2.11:jar:10.1.7:compile
[INFO] |  |  \- com.typesafe.akka:akka-parsing_2.11:jar:10.1.7:compile
[INFO] |  +- com.typesafe.akka:akka-http-spray-json_2.11:jar:10.1.7:compile
[INFO] |  |  \- io.spray:spray-json_2.11:jar:1.3.5:compile
[INFO] |  +- org.aspectj:aspectjrt:jar:1.9.7:compile
[INFO] |  +- com.google.code.gson:gson:jar:2.9.1:compile
[INFO] |  +- com.typesafe.akka:akka-stream-kafka_2.11:jar:1.0.4:compile
[INFO] |  +- com.typesafe.akka:akka-remote_2.11:jar:2.5.25:compile
[INFO] |  |  +- io.aeron:aeron-driver:jar:1.15.1:compile
[INFO] |  |  |  \- org.agrona:agrona:jar:0.9.31:compile
[INFO] |  |  \- io.aeron:aeron-client:jar:1.15.1:compile
[INFO] |  +- org.slf4j:slf4j-api:jar:1.7.36:compile
[INFO] |  +- io.jaegertracing:jaeger-client:jar:1.0.0:compile
[INFO] |  |  +- io.jaegertracing:jaeger-thrift:jar:1.0.0:compile
[INFO] |  |  |  +- org.apache.thrift:libthrift:jar:0.12.0:compile
[INFO] |  |  |  \- com.squareup.okhttp3:okhttp:jar:4.9.3:compile
[INFO] |  |  |     +- com.squareup.okio:okio:jar:2.8.0:compile
[INFO] |  |  |     |  \- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.6.21:compile
[INFO] |  |  |     \- org.jetbrains.kotlin:kotlin-stdlib:jar:1.6.21:compile
[INFO] |  |  |        \- org.jetbrains:annotations:jar:13.0:compile
[INFO] |  |  +- io.jaegertracing:jaeger-core:jar:1.0.0:compile
[INFO] |  |  |  +- io.opentracing:opentracing-api:jar:0.33.0:compile
[INFO] |  |  |  \- io.opentracing:opentracing-util:jar:0.33.0:compile
[INFO] |  |  |     \- io.opentracing:opentracing-noop:jar:0.33.0:compile
[INFO] |  |  \- io.jaegertracing:jaeger-tracerresolver:jar:1.0.0:compile
[INFO] |  |     \- io.opentracing.contrib:opentracing-tracerresolver:jar:0.1.8:compile
[INFO] |  +- com.google.guava:guava:jar:18.0:compile
[INFO] |  \- com.typesafe:config:jar:1.3.0:compile
[INFO] +- com.ppb.platform.stream:no-op-persistence:jar:4.1.0_3.3_2.11-SNAPSHOT:compile
[INFO] |  \- com.typesafe.akka:akka-persistence_2.11:jar:2.5.25:compile
[INFO] +- org.springframework.boot:spring-boot-starter-test:jar:2.7.8:test
[INFO] |  +- org.springframework.boot:spring-boot-test:jar:2.7.8:test
[INFO] |  +- org.springframework.boot:spring-boot-test-autoconfigure:jar:2.7.8:test
[INFO] |  +- com.jayway.jsonpath:json-path:jar:2.7.0:test
[INFO] |  |  \- net.minidev:json-smart:jar:2.4.8:test
[INFO] |  |     \- net.minidev:accessors-smart:jar:2.4.8:test
[INFO] |  |        \- org.ow2.asm:asm:jar:9.1:test
[INFO] |  +- jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.3:test
[INFO] |  |  \- jakarta.activation:jakarta.activation-api:jar:1.2.2:test
[INFO] |  +- org.assertj:assertj-core:jar:3.22.0:test
[INFO] |  +- org.hamcrest:hamcrest:jar:2.2:test
[INFO] |  +- org.junit.jupiter:junit-jupiter:jar:5.8.2:test
[INFO] |  |  +- org.junit.jupiter:junit-jupiter-api:jar:5.8.2:test
[INFO] |  |  |  +- org.opentest4j:opentest4j:jar:1.2.0:test
[INFO] |  |  |  +- org.junit.platform:junit-platform-commons:jar:1.8.2:test
[INFO] |  |  |  \- org.apiguardian:apiguardian-api:jar:1.1.2:test
[INFO] |  |  +- org.junit.jupiter:junit-jupiter-params:jar:5.8.2:test
[INFO] |  |  \- org.junit.jupiter:junit-jupiter-engine:jar:5.8.2:test
[INFO] |  |     \- org.junit.platform:junit-platform-engine:jar:1.8.2:test
[INFO] |  +- org.mockito:mockito-core:jar:4.5.1:test
[INFO] |  |  +- net.bytebuddy:byte-buddy:jar:1.12.22:test
[INFO] |  |  +- net.bytebuddy:byte-buddy-agent:jar:1.12.22:test
[INFO] |  |  \- org.objenesis:objenesis:jar:3.2:compile
[INFO] |  +- org.mockito:mockito-junit-jupiter:jar:4.5.1:test
[INFO] |  +- org.skyscreamer:jsonassert:jar:1.5.1:test
[INFO] |  |  \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:test
[INFO] |  +- org.springframework:spring-test:jar:5.3.25:test
[INFO] |  \- org.xmlunit:xmlunit-core:jar:2.9.1:test
[INFO] +- com.flutter:protobuf-extension:jar:1.0-SNAPSHOT:compile
[INFO] |  +- com.google.protobuf:protobuf-java:jar:3.21.12:compile
[INFO] |  \- com.google.protobuf:protobuf-java-util:jar:3.21.12:compile
[INFO] |     +- com.google.errorprone:error_prone_annotations:jar:2.5.1:compile
[INFO] |     +- com.google.j2objc:j2objc-annotations:jar:1.3:compile
[INFO] |     \- com.google.code.findbugs:jsr305:jar:3.0.2:compile
[INFO] +- com.ppb.platform.sb:market-stream-model:jar:1.46.0:compile
[INFO] |  +- com.betfair.sportsbook:sportsbook-domain:jar:1.2.27:compile
[INFO] |  |  \- com.ppb.platform.sb:ppb-sportsbook-domain:jar:2.05:compile
[INFO] |  +- commons-lang:commons-lang:jar:2.6:compile
[INFO] |  +- org.apache.commons:commons-lang3:jar:3.12.0:compile
[INFO] |  +- org.apache.commons:commons-collections4:jar:4.1:compile
[INFO] |  \- joda-time:joda-time:jar:2.3:compile
[INFO] +- com.ppb.platform.sb:market-stream-serializers:jar:1.46.0:compile
[INFO] |  +- com.ppb.platform.sb:market-stream-message-definition:jar:1.46.0:compile
[INFO] |  +- com.ppb.platform.sb:journal-serialization:jar:1.21:compile
[INFO] |  |  \- com.betfair.platform.sb:platform-utilities:jar:2.47:compile
[INFO] |  |     \- com.esotericsoftware:kryo-shaded:jar:3.0.2:compile
[INFO] |  +- com.ppb.platform.sb:ppb-sb-feature-toggles-core:jar:1.6:compile
[INFO] |  \- com.ppb.platform.sb:pricing-utils:jar:6.0:compile
[INFO] |     \- com.ppb.platform.sb:market-model:jar:6.0:compile
[INFO] +- com.ppb.platform.sb:market-stream-client:jar:4.17.0:compile
[INFO] |  +- com.ppb.platform.sb:market-stream-client-common:jar:1.48.0:compile
[INFO] |  |  +- com.betfair.monitoring:monitor:jar:2.8.3:compile
[INFO] |  |  |  +- commons-io:commons-io:jar:1.4:compile
[INFO] |  |  |  \- cglib:cglib-nodep:jar:2.1_3:compile
[INFO] |  |  +- com.betfair.platform:cougar-api:jar:4.12.8:compile
[INFO] |  |  |  +- com.betfair.platform:virtualheap:jar:1.2:compile
[INFO] |  |  |  \- com.paddypowerbetfair.libraries:business-context-propagation:jar:1.0.0:compile
[INFO] |  |  \- com.ppb.platform.sb:ppb-sb-executors:jar:1.7:compile
[INFO] |  |     \- commons-collections:commons-collections:jar:3.2.2:compile
[INFO] |  +- com.ppb.platform.sb:market-stream-metrics:jar:1.48.0:compile
[INFO] |  +- com.ppb.platform.sb:market-stream-metrics-kamon:jar:1.48.0:compile
[INFO] |  +- com.betfair.platform:cougar-util:jar:4.12.9:compile
[INFO] |  |  +- org.slf4j:jcl-over-slf4j:jar:1.7.36:compile
[INFO] |  |  +- org.springframework:spring-aspects:jar:5.3.25:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:5.3.25:compile
[INFO] |  |  +- org.springframework:spring-context-support:jar:5.3.25:compile
[INFO] |  |  +- org.springframework:spring-tx:jar:5.3.25:compile
[INFO] |  |  +- com.betfair.monitoring:kpi-quartzpublisher:jar:2.10.2:compile
[INFO] |  |  |  +- com.betfair.monitoring:kpi-base-spring:jar:2.10.2:compile
[INFO] |  |  |  |  \- com.betfair.monitoring:kpi-base:jar:2.10.2:compile
[INFO] |  |  |  |     \- com.jamonapi:jamon:jar:2.75:compile
[INFO] |  |  |  \- org.quartz-scheduler:quartz:jar:2.3.2:compile
[INFO] |  |  |     \- com.mchange:mchange-commons-java:jar:0.2.15:compile
[INFO] |  |  +- com.betfair.libraries:reloadable-configs:jar:0.4:compile
[INFO] |  |  +- com.maxmind.geoip2:geoip2:jar:2.16.1:compile
[INFO] |  |  |  +- com.maxmind.db:maxmind-db:jar:2.0.0:compile
[INFO] |  |  |  \- commons-codec:commons-codec:jar:1.15:compile
[INFO] |  |  +- javax.servlet:javax.servlet-api:jar:4.0.1:compile
[INFO] |  |  +- com.sun.jdmk:jmxtools:jar:1.2.1:compile
[INFO] |  |  +- org.jasypt:jasypt:jar:1.9.0:compile
[INFO] |  |  \- org.jasypt:jasypt-spring2:jar:1.9.0:compile
[INFO] |  +- org.springframework:spring-beans:jar:5.3.25:compile
[INFO] |  +- org.scala-lang.modules:scala-java8-compat_2.11:jar:0.7.0:compile
[INFO] |  +- com.ppb.platform.sb:ppb-sb-metrics-core:jar:1.7:compile
[INFO] |  +- com.esotericsoftware:kryo:jar:5.6.2:compile
[INFO] |  |  +- com.esotericsoftware:reflectasm:jar:1.11.9:compile
[INFO] |  |  \- com.esotericsoftware:minlog:jar:1.3.1:compile
[INFO] |  \- de.javakaffee:kryo-serializers:jar:0.45:compile
[INFO] +- org.springframework.boot:spring-boot-starter-tomcat:jar:2.7.8:compile
[INFO] |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:9.0.71:compile
[INFO] |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:9.0.71:compile
[INFO] |  \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:9.0.71:compile
[INFO] \- com.ppb.platform.sb:ppb-sb-metrics-mantis:jar:1.7:compile
[INFO]    +- com.betfair.mantis.performance:mantis-performance:jar:1.14.6:compile
[INFO]    |  +- org.springframework:spring-web:jar:5.3.25:compile
[INFO]    |  +- com.betfair.sre:statse-client:jar:1.3.0:compile
[INFO]    |  |  \- org.zeromq:jeromq:jar:0.3.3:compile
[INFO]    |  +- org.codehaus.jackson:jackson-core-asl:jar:1.8.1:compile
[INFO]    |  +- com.googlecode.disruptor:disruptor:jar:2.8:compile
[INFO]    |  +- org.apache.httpcomponents:httpclient:jar:4.5.14:compile
[INFO]    |  +- org.apache.httpcomponents:httpcore:jar:4.4.16:compile
[INFO]    |  \- org.apache.commons:commons-math:jar:2.2:compile
[INFO]    +- org.aspectj:aspectjweaver:jar:1.9.7:compile
[INFO]    +- com.betfair.platform:cougar-core-api:jar:4.10.3:compile
[INFO]    |  \- javax.ws.rs:jsr311-api:jar:1.1:compile
[INFO]    \- com.betfair.platform.sb:cougar-commons:jar:2.40:compile
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  20:59 min
[INFO] Finished at: 2025-09-16T18:44:52+03:00
[INFO] ------------------------------------------------------------------------
