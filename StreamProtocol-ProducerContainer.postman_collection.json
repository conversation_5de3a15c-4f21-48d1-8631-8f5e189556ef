{"info": {"_postman_id": "f908c85d-c03f-46e1-86df-e71df350c224", "name": "SP", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "3236907"}, "item": [{"name": "Create", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"dgojSportCode\": \"56\",\n    \"dgojSportName\": \"Volleyball\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8080", "host": ["localhost"], "port": "8080"}}, "response": []}, {"name": "Snapshot", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8080/snapshot", "host": ["localhost"], "port": "8080", "path": ["snapshot"]}}, "response": []}, {"name": "Get", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8080", "host": ["localhost"], "port": "8080"}}, "response": []}]}