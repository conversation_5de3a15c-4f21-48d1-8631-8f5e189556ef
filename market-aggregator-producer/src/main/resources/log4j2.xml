<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">
    <Properties>
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd'T'HH:mm:ss} %p %c %m%n</Property>
    </Properties>

    <Appenders>
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="${LOG_PATTERN}" />
        </Console>
    </Appenders>

    <Loggers>
        <Root level="info">
            <AppenderRef ref="Console" />
        </Root>

        <Logger name="com.ppb.platform.stream" level="info"/>
        <Logger name="org.apache.kafka" level="warn"/>
        <Logger name="org.apache.zookeeper" level="warn"/>
    </Loggers>
</Configuration>