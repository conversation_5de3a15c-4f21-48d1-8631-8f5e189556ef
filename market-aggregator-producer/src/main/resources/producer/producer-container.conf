akka {
  loggers = [akka.event.slf4j.Slf4jLogger]
  log-dead-letters = on
  log-dead-letters-during-shutdown = true
  loglevel = DEBUG
  # Log the complete configuration at INFO level when the actor system is started.
  # This is useful when you are uncertain of what configuration is used.
  log-config-on-start = off
  actor {
    provider = akka.cluster.ClusterActorRefProvider
    debug {
      lifecycle = on
      unhandled = on
      receive = on
      # enable DEBUG logging of all AutoReceiveMessages (Kill, PoisonPill et.c.)
      autoreceive = on
      # enable DEBUG logging of subscription changes on the eventStream
      event-stream = on
    }
  }
  log-dead-letters = on

  persistence {
    journal {
      plugin = "noOp-journal"
    }
    snapshot-store {
      plugin = "noOp-snapshot-store"
    }
  }

  quartz {
    schedules {
      SnapshotCron {
        description = "A cron job that fires off a snapshot"
        expression = "0 0 * ? * * *"
        timezone = "UTC"
      }
    }
  }
}

akka.test.single-expect-default = 2 seconds
// Producer container specific configuration
producer-container {
  bootstrap {
    init-timeout = 90 seconds
  }
  persistence {
    number-of-messages-to-snapshot = 3
    notification.buffer.size = 1
    supervisor {
      min-backoff = 3 seconds
      max-backoff = 30 seconds
      random-factor = 0.2
      // This config must be FiniteDuration that is between or equals
      // to min-backoff and max-backoff or this will caused the container to fail
      reset-backoff = 30 seconds
    }
  }
  producer {
    init-timeout = 90 seconds
    query-state-timeout = 10 seconds
    snapshot-manager {
      min-failure-interval-snapshot = 5 minutes
      schedule-name = "SnapshotCron"
      start-delay = 5000 milliseconds
    }
  }

  tracing {
    // Values:
    // forced - Enables/Forces tracing
    // enabled - Traces if traceId is present
    // disabled - Disables tracing
    name = ${producer-container.name}
    trace = forced
    jaeger {
      hostname = "127.0.0.1"
      port = 6831
      samplerType = "remote"
      samplerParam = 1
      logSpans = false
      metrics {
        enalbed = true
      }
    }
  }
}

jmx {
  base.name = "com.ppb.platform.stream.protocol.producerContainer"
}

metrics {
  enabled = false
}
