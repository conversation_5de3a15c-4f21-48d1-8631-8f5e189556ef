package com.flutter.producer.controller;

import com.flutter.producer.converter.MappingConverter;
import com.flutter.producer.dto.MappingDto;
import com.flutter.producer.persistence.model.MappingEntity;
import com.flutter.producer.persistence.repo.MappingEntityRepository;
import com.isp.platform.mas.messages.Common;
import com.ppb.platform.stream.model.ProducerInstructionType;
import com.ppb.platform.stream.producer.ProducerContainer;
import com.ppb.platform.stream.producer.core.model.ContainerData;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import scala.Option;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Slf4j
@RestController
@RequiredArgsConstructor
public class MappingController {

    private final MappingEntityRepository mappingEntityRepository;
    private final ProducerContainer<Common.Mappings, Common.Mappings> producerContainer;

    @PostMapping
    public ResponseEntity<List<MappingDto>> store(@RequestBody MappingDto mappingDto) {
        Common.Mappings mappings = MappingConverter.toMappingsProto(mappingDto);
        MappingEntity entity = MappingConverter.toMappingEntity(mappings);

        MappingEntity save = mappingEntityRepository.save(entity);
        log.info("store: Stored entity {}", save);

        Common.Mappings mappingMessage = MappingConverter.toMappingsProto(save);
        ContainerData<Common.Mappings> containerData =
                new ContainerData<>(mappingMessage.getSpainMapping().getDgojSportCode(), Option.apply(mappingMessage));

        producerContainer.produce(ProducerInstructionType.DELTA, Option.empty(), containerData);
        return retrieve();
    }

    @GetMapping ResponseEntity<List<MappingDto>> retrieve() {
        Iterable<MappingEntity> all = mappingEntityRepository.findAll();
        List<MappingDto> result = StreamSupport.stream(all.spliterator(), false)
                .map(MappingConverter::toMappingsProto)
                .map(MappingConverter::toMappingDto)
                .collect(Collectors.toList());

        return ResponseEntity.ok(result);
    }

    @PostMapping("/snapshot")
    public ResponseEntity<Void> forceSnapshot() {
        producerContainer.service().forceSnapshot();

        return ResponseEntity.accepted().build();
    }

}
