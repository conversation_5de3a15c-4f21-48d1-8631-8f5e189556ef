package com.flutter.producer.config.sp;

import com.flutter.producer.config.props.ProducerProperties;
import com.flutter.producer.factory.ProducerContainerFactory;
import com.flutter.producer.stream.MappingsEntitySerializer;
import com.flutter.producer.stream.MappingsPayloadSerializer;
import com.flutter.producer.stream.MappingsDeltaApplier;
import com.flutter.producer.stream.MappingsDataSource;
import com.isp.platform.mas.messages.Common;
import com.ppb.platform.stream.producer.ProducerContainer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import scala.Option;

@Configuration
public class ProducerConfig {

    @Bean
    public ProducerContainer<Common.Mappings, Common.Mappings> producerContainer(
            ProducerProperties producerProperties,
            MappingsDeltaApplier mappingsDeltaApplier,
            MappingsDataSource mappingsDataSource,
            MappingsPayloadSerializer mappingsPayloadSerializer,
            MappingsEntitySerializer mappingsEntitySerializer
    ) {
        return ProducerContainerFactory.newInstance(
                producerProperties,
                mappingsDeltaApplier,
                mappingsDataSource,
                mappingsPayloadSerializer,
                mappingsEntitySerializer,
                Option.empty()
        );
    }

}
