package com.flutter.producer.config.props;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "sp.producer")
public class ProducerProperties {

    private String bootstrapServers;
    private String zookeeperServers;
    private String topic;
    private int akkaTcpPort;
    private int akkaHttpPort;
    private boolean coldStart;
}
