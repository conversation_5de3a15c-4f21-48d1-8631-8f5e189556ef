package com.flutter.producer.stream;

import com.isp.platform.mas.messages.Common;
import com.ppb.platform.stream.producer.core.persistence.EntitySerializer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Slf4j
@Component
public class MappingsEntitySerializer implements EntitySerializer<Common.Mappings> {

    private final MappingsPayloadSerializer ser;
    private final MappingsPayloadDeserializer deser;

    @Override
    public byte[] serialize(Common.Mappings from) {
        return ser.serialize(from);
    }

    @Override
    public Common.Mappings deserialize(byte[] from) {
        return deser.deserialize(from);
    }
}
