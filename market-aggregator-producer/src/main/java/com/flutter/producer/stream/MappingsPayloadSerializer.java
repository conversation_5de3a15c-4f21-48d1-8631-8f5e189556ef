package com.flutter.producer.stream;

import com.isp.platform.mas.messages.Common;
import com.ppb.platform.stream.serialization.PayloadSerializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MappingsPayloadSerializer implements PayloadSerializer<Common.Mappings> {
    @Override
    public byte[] serialize(Common.Mappings from) {
        return from.toByteArray();
    }
}
