package com.flutter.producer.stream;

import com.isp.platform.mas.messages.Common;
import com.ppb.platform.stream.model.DeltaApplierInstructionType;
import com.ppb.platform.stream.model.context.ContextHolder;
import com.ppb.platform.stream.producer.core.deltaapplier.DeltaApplier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import scala.Option;
import scala.Tuple2;

@Slf4j
@Component
public class MappingsDeltaApplier implements DeltaApplier<Common.Mappings, Common.Mappings> {

    @Override
    public Tuple2<Common.Mappings, Option<Common.Mappings>> apply(
            DeltaApplierInstructionType instructionType,
            Option<ContextHolder> appCtx,
            Option<Common.Mappings> base,
            Common.Mappings delta
    ) {
        log.info("apply: applying delta {}, {}, {}, {}", instructionType, appCtx, base, delta);
        return new Tuple2<>(delta, Option.apply(delta));
    }
}
