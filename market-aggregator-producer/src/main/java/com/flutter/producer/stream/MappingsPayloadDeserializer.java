package com.flutter.producer.stream;

import com.isp.platform.mas.messages.Common;
import com.ppb.platform.stream.serialization.PayloadDeserializer;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

@Component
public class MappingsPayloadDeserializer implements PayloadDeserializer<Common.Mappings> {

    @Override
    @SneakyThrows
    public Common.Mappings deserialize(byte[] from) {
        return Common.Mappings.parseFrom(from);
    }
}
