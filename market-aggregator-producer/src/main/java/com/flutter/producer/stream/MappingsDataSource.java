package com.flutter.producer.stream;

import com.flutter.producer.config.props.ProducerProperties;
import com.flutter.producer.converter.MappingConverter;
import com.flutter.producer.persistence.model.MappingEntity;
import com.flutter.producer.persistence.repo.MappingEntityRepository;
import com.isp.platform.mas.messages.Common;
import com.ppb.platform.stream.producer.core.model.ContainerData;
import com.ppb.platform.stream.producer.management.coldstart.datasource.DataSource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import scala.Option;
import scala.collection.JavaConversions;
import scala.collection.immutable.List;

import java.util.Collections;
import java.util.Spliterator;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Component
@RequiredArgsConstructor
public class MappingsDataSource implements DataSource<Common.Mappings> {

    private final MappingEntityRepository mappingEntityRepository;
    private final ProducerProperties producerProperties;

    @Override
    public List<ContainerData<Common.Mappings>> coldStart() {
        if (!producerProperties.isColdStart()) {
            return JavaConversions.asScalaBuffer(Collections.<ContainerData<Common.Mappings>>emptyList()).toList();
        }

        Spliterator<MappingEntity> mappingEntitySpliterator = mappingEntityRepository.findAll().spliterator();
        java.util.List<ContainerData<Common.Mappings>> mappings =
                StreamSupport.stream(mappingEntitySpliterator, false)
                .map(entity ->
                        new ContainerData<>(
                                "" + entity.getId(),
                                Option.apply(MappingConverter.toMappingsProto(entity)))
                )
                .collect(Collectors.toList());

        return JavaConversions.asScalaBuffer(mappings).toList();
    }
}



