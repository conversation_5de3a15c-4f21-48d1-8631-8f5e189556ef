package com.flutter.producer.converter;

import com.flutter.producer.dto.MappingDto;
import com.flutter.producer.persistence.model.MappingEntity;
import com.isp.platform.mas.messages.Common;
import lombok.SneakyThrows;

import javax.sql.rowset.serial.SerialBlob;
import java.sql.Blob;
import java.time.Instant;
import java.util.Objects;

public class MappingConverter {

    public static Common.Mappings toMappingsProto(MappingDto mappingDto) {
        Common.SpainMapping.Builder b = Common.SpainMapping.newBuilder();
        if (Objects.nonNull(mappingDto.getDgojEventType())) {
            b.setDgojEventType(mappingDto.getDgojEventType());
        }
        if (Objects.nonNull(mappingDto.getDgojSportName())) {
            b.setDgojSportName(mappingDto.getDgojSportName());
        }
        if (Objects.nonNull(mappingDto.getDgojSportCode())) {
            b.setDgojSportCode(mappingDto.getDgojSportCode());
        }
        if (Objects.nonNull(mappingDto.getDgojCompetitionCountryISO())) {
            b.setDgojCompetitionCountryIso(mappingDto.getDgojCompetitionCountryISO());
        }
        if (Objects.nonNull(mappingDto.getDgojCompetitionGender())) {
            b.setDgojCompetitionGender(mappingDto.getDgojCompetitionGender());
        }
        if (Objects.nonNull(mappingDto.getDgojCompetitionCategory())) {
            b.setDgojCompetitionCategory(mappingDto.getDgojCompetitionCategory());
        }
        if (Objects.nonNull(mappingDto.getDgojSpecialEvent())) {
            b.setDgojSpecialEvent(mappingDto.getDgojSpecialEvent());
        }
        if (Objects.nonNull(mappingDto.getMultiEventLeg())) {
            b.setMultiEventLeg(mappingDto.getMultiEventLeg());
        }
        if (Objects.nonNull(mappingDto.getMultiCompetitionLeg())) {
            b.setMultiCompetitionLeg(mappingDto.getMultiCompetitionLeg());
        }
        Instant now = Instant.now();
        b.setEventEndDate(
                Common.Timestamp.newBuilder()
                        .setSeconds(now.getEpochSecond())
                        .setNanos(now.getNano())
                        .build()
        );

        return Common.Mappings.newBuilder()
                .setSpainMapping(b.build())
                .build();
    }

    public static MappingDto toMappingDto(Common.Mappings mappings) {
        MappingDto.MappingDtoBuilder builder = MappingDto.builder();

        if (mappings.hasSpainMapping()) {
            Common.SpainMapping spainMapping = mappings.getSpainMapping();
            builder.dgojEventType(spainMapping.getDgojEventType());
            builder.dgojSportCode(spainMapping.getDgojSportCode());
            builder.dgojSportName(spainMapping.getDgojSportName());
            builder.dgojCompetitionCountryISO(spainMapping.getDgojCompetitionCountryIso());
            builder.dgojCompetitionGender(spainMapping.getDgojCompetitionGender());
            builder.dgojCompetitionCategory(spainMapping.getDgojCompetitionCategory());
            builder.dgojSpecialEvent(spainMapping.getDgojSpecialEvent());
            builder.multiEventLeg(spainMapping.getMultiEventLeg());
            builder.multiCompetitionLeg(spainMapping.getMultiCompetitionLeg());
        }

        return builder.build();
    }

    @SneakyThrows
    public static Common.Mappings toMappingsProto(MappingEntity mappingEntity) {
        Common.SpainMapping mapping = Common.SpainMapping.newBuilder().build();

        Blob payload = mappingEntity.getPayload();
        if (payload != null) {
            mapping = Common.SpainMapping.parseFrom(payload.getBinaryStream());
        }

        return Common.Mappings.newBuilder()
                .setSpainMapping(mapping)
                .build();
    }

    @SneakyThrows
    public static MappingEntity toMappingEntity(Common.Mappings mappings) {
        MappingEntity entity = new MappingEntity();

        if (mappings.hasSpainMapping()) {
            Common.SpainMapping mapping = mappings.getSpainMapping();
            SerialBlob blob = new SerialBlob(mapping.toByteArray());
            entity.setPayload(blob);
            entity.setId(Integer.parseInt(mapping.getDgojSportCode()));
        }

        return entity;
    }
}
