package com.flutter.producer.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MappingDto {

    private String dgojSportCode;
    private String dgojSportName;
    private String dgojEventType;
    private String dgojCompetitionCountryISO;
    private String dgojCompetitionGender;
    private String dgojCompetitionCategory;
    private String dgojSpecialEvent;
    private String multiEventLeg;
    private String multiCompetitionLeg;

}
