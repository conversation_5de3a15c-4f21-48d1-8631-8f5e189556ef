package com.flutter.producer.factory;

import com.flutter.producer.config.props.ProducerProperties;
import com.ppb.platform.stream.producer.ProducerContainer;
import com.ppb.platform.stream.producer.core.deltaapplier.DeltaApplier;
import com.ppb.platform.stream.producer.core.persistence.EntitySerializer;
import com.ppb.platform.stream.producer.management.coldstart.datasource.DataSource;
import com.ppb.platform.stream.serialization.PayloadSerializer;
import com.typesafe.config.Config;
import com.typesafe.config.ConfigFactory;
import scala.Option;

import java.util.Collections;
import java.util.Random;

import static com.typesafe.config.ConfigValueFactory.fromAnyRef;
import static com.typesafe.config.ConfigValueFactory.fromIterable;

public class ProducerContainerFactory {

    public static <T, S> ProducerContainer<T, S> newInstance(
            ProducerProperties producerProperties,
            DeltaApplier<T, S> deltaApplier,
            DataSource<T> dataSource,
            PayloadSerializer<S> payloadSerializer,
            EntitySerializer<T> entitySerializer,
            Option<Config> producerOverrideConfigs
    ) {
        Config producerContainerConfig = baseContainerProducerConfig(producerProperties);
        if (producerOverrideConfigs.isDefined()) {
            producerContainerConfig = producerOverrideConfigs.get().withFallback(producerContainerConfig);
        }

        Config producerConfig = ConfigFactory.parseResources("producer/producer.conf")
                .withValue("producer.bootstrap.servers", fromAnyRef(producerProperties.getBootstrapServers()))
                .withValue("producer.topic", fromAnyRef(producerProperties.getTopic()));

        return new ProducerContainer<>(
                producerConfig,
                Option.apply(producerContainerConfig),
                dataSource,
                payloadSerializer,
                deltaApplier,
                entitySerializer);

    }

    private static Config baseContainerProducerConfig(ProducerProperties producerProperties) {
        int httpPort = producerProperties.getAkkaHttpPort();
        int tcpPort = producerProperties.getAkkaTcpPort();
        String topic = producerProperties.getTopic();
        String zkHost = producerProperties.getZookeeperServers();

        return ConfigFactory.parseResources("producer/producer-container.conf")
                .withValue("producer-container.bootstrap.metadata-producer.zk-endpoints",
                        fromAnyRef(zkHost))
                .withValue("producer-container.bootstrap.metadata-producer.zNodePath",
                        fromAnyRef('/' + topic))
                .withValue("akka.persistence.journal.leveldb.dir",
                        fromAnyRef("target/shared/" + topic + "/" + new Random().nextInt() ))
                .withValue("akka.remote.netty.tcp.port",
                        fromAnyRef(tcpPort))
                .withValue("akka.cluster.seed-nodes",
                        fromIterable(Collections.singleton("akka.tcp://ProducerContainer@127.0.0.1:" + tcpPort)))
                .withValue("producer-container.bootstrap.http.port",
                        fromAnyRef(httpPort));
    }
}
