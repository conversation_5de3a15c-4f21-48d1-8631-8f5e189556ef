package com.flutter.producer;

import com.isp.platform.mas.messages.Common;
import com.ppb.platform.stream.container.ContainerStatus;
import com.ppb.platform.stream.producer.ProducerContainer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class BootstrapService {

    private final ProducerContainer<Common.Mappings, Common.Mappings> producerContainer;

    @EventListener(ApplicationReadyEvent.class)
    public void handleApplicationReadyEvent() {
        log.info("handleApplicationReadyEvent: Starting Producer Container...");
        ContainerStatus start = producerContainer.start();
        log.info("handleApplicationReadyEvent: ContainerStatus({})", start);
    }
}
