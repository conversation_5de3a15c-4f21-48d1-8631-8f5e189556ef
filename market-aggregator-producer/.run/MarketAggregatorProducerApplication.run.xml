<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="MarketAggregatorProducerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
    <module name="market-aggregator-producer" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.flutter.producer.MarketAggregatorProducerApplication" />
    <option name="VM_PARAMETERS" value="--illegal-access=debug --add-opens=java.base/java.nio=ALL-UNNAMED" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>