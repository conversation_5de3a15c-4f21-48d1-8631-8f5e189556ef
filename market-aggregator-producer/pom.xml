<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.8</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.flutter</groupId>
    <artifactId>market-aggregator-producer</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>market-aggregator-producer</name>
    <description>market-aggregator-producer</description>
    <properties>
        <java.version>11</java.version>

        <scala-major.version>2.11</scala-major.version>
        <scala-patch.version>12</scala-patch.version>
        <scala.version>${scala-major.version}.${scala-patch.version}</scala.version>

        <kafka-major.version>2.3</kafka-major.version>
        <kafka-minor.version>1</kafka-minor.version>

        <!-- plugin dependencies -->
        <scala-maven-plugin.version>3.3.3</scala-maven-plugin.version>

        <akka.major.version>2</akka.major.version>
        <akka.minor.version>5</akka.minor.version>
        <akka.patch.version>25</akka.patch.version>
        <akka.majorminor.version>${akka.major.version}.${akka.minor.version}</akka.majorminor.version>
        <akka.version>${akka.majorminor.version}.${akka.patch.version}</akka.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.29</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.ppb.platform.stream</groupId>
            <artifactId>stream-protocol-producer</artifactId>
            <version>4.1.0_2.3_2.11</version>
        </dependency>
        <dependency>
            <groupId>com.ppb.platform.stream</groupId>
            <artifactId>no-op-persistence</artifactId>
            <version>4.1.0_2.3_2.11</version>
        </dependency>
        <dependency>
            <groupId>com.ppb.platform.sb</groupId>
            <artifactId>market-stream-model</artifactId>
            <version>1.46.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.flutter</groupId>
            <artifactId>protobuf-extension</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <version>${scala-maven-plugin.version}</version>
                <configuration>
                    <scalaCompatVersion>${scala-major.version}</scalaCompatVersion>
                    <scalaVersion>${scala.version}</scalaVersion>
                    <recompileMode>incremental</recompileMode>
                    <jvmArgs>
                        <jvmArg>-Xms256m</jvmArg>
                        <jvmArg>-Xmx2048m</jvmArg>
                    </jvmArgs>
                    <args>
                        <!--<arg>-Xfatal-warnings</arg>-->
                        <arg>-deprecation</arg>
                        <arg>-Xlint</arg>
                        <arg>-feature</arg>
                        <arg>-language:postfixOps</arg>
                        <!--<arg>-Yno-adapted-args</arg>-->
                        <arg>-Ywarn-adapted-args</arg>
                        <arg>-Ywarn-dead-code</arg>
                        <arg>-Ywarn-infer-any</arg>
                        <arg>-Ywarn-unused-import</arg>
                        <arg>-D-unused-import</arg>
                    </args>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>add-source</goal>
                            <goal>compile</goal>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
